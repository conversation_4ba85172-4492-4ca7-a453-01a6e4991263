// TODO: Install @whop/api package when needed
// import { WhopServerSdk } from "@whop/api";

const appId = process.env.NEXT_PUBLIC_WHOP_APP_ID;
const appApiKey = process.env.WHOP_API_KEY;

if (!appId || !appApiKey) {
  console.warn("Missing NEXT_PUBLIC_WHOP_APP_ID or WHOP_API_KEY");
}

// TODO: Uncomment when @whop/api is installed
// export const whopSdk = WhopServerSdk({
//   appId,
//   appApiKey,
// });

export const whopSdk = null; // Placeholder until package is installed