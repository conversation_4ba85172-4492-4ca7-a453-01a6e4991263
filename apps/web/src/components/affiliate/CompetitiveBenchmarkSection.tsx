'use client';

import { UsersIcon, TrendingUpIcon, TrendingDownIcon, MinusIcon } from 'lucide-react';
import { MagicWand01 as BrainIcon } from '@untitled-ui/icons-react';

interface Creator {
  id: string;
  username: string;
  displayName?: string;
  followerCount: number;
  engagementRate: number;
  profileImageUrl?: string;
}

interface CompetitiveBenchmark {
  id: string;
  metric: string;
  userValue: number;
  competitorValue: number;
  period: string;
  date: string;
}

interface CompetitiveBenchmarkSectionProps {
  benchmarks: Array<{
    competitor: Creator;
    benchmark: CompetitiveBenchmark;
    performance: 'ahead' | 'behind' | 'tied';
  }>;
  userId: string;
  onGenerateAnalysis: () => void;
}

export function CompetitiveBenchmarkSection({ benchmarks, onGenerateAnalysis }: CompetitiveBenchmarkSectionProps) {
  const formatValue = (value: number, metric: string) => {
    switch (metric) {
      case 'gmv':
      case 'commissions':
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
          minimumFractionDigits: 0,
          maximumFractionDigits: 0,
        }).format(value);
      case 'conversion_rate':
        return `${value.toFixed(2)}%`;
      case 'follower_count':
        return value.toLocaleString();
      default:
        return value.toLocaleString();
    }
  };

  const getPerformanceIcon = (performance: string) => {
    switch (performance) {
      case 'ahead': return TrendingUpIcon;
      case 'behind': return TrendingDownIcon;
      case 'tied': return MinusIcon;
      default: return MinusIcon;
    }
  };

  const getPerformanceColor = (performance: string) => {
    switch (performance) {
      case 'ahead': return 'text-green-600 bg-green-100';
      case 'behind': return 'text-red-600 bg-red-100';
      case 'tied': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getMetricLabel = (metric: string) => {
    switch (metric) {
      case 'gmv': return 'GMV';
      case 'commissions': return 'Commissions';
      case 'conversion_rate': return 'Conversion Rate';
      case 'follower_count': return 'Followers';
      case 'engagement_rate': return 'Engagement Rate';
      default: return metric.replace('_', ' ').toUpperCase();
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-2xl font-bold text-gray-900">Competitive Benchmarks</h3>
          <p className="text-gray-600">See how you stack up against competitors</p>
        </div>
        
        <button
          onClick={onGenerateAnalysis}
          className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
          data-rounded="default"
        >
          <BrainIcon className="w-4 h-4 mr-2" />
          AI Gap Analysis
        </button>
      </div>

      {benchmarks.length > 0 ? (
        <div className="space-y-6">
          {/* Performance Summary */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white rounded-lg shadow-sm p-6 text-center">
              <TrendingUpIcon className="w-8 h-8 text-green-600 mx-auto mb-2" />
              <p className="text-2xl font-bold text-gray-900">
                {benchmarks.filter(b => b.performance === 'ahead').length}
              </p>
              <p className="text-sm text-gray-600">Metrics Ahead</p>
            </div>
            
            <div className="bg-white rounded-lg shadow-sm p-6 text-center">
              <TrendingDownIcon className="w-8 h-8 text-red-600 mx-auto mb-2" />
              <p className="text-2xl font-bold text-gray-900">
                {benchmarks.filter(b => b.performance === 'behind').length}
              </p>
              <p className="text-sm text-gray-600">Metrics Behind</p>
            </div>
            
            <div className="bg-white rounded-lg shadow-sm p-6 text-center">
              <MinusIcon className="w-8 h-8 text-yellow-600 mx-auto mb-2" />
              <p className="text-2xl font-bold text-gray-900">
                {benchmarks.filter(b => b.performance === 'tied').length}
              </p>
              <p className="text-sm text-gray-600">Metrics Tied</p>
            </div>
          </div>

          {/* Benchmarks Table */}
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h4 className="text-lg font-medium text-gray-900">Detailed Comparisons</h4>
            </div>
            
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Competitor
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Metric
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Your Value
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Their Value
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Performance
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {benchmarks.map((item, index) => {
                    const Icon = getPerformanceIcon(item.performance);
                    
                    return (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            {item.competitor.profileImageUrl ? (
                              <img 
                                className="h-10 w-10 rounded-full mr-3" 
                                src={item.competitor.profileImageUrl} 
                                alt={item.competitor.username}
                              />
                            ) : (
                              <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center mr-3">
                                <UsersIcon className="h-5 w-5 text-gray-500" />
                              </div>
                            )}
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {item.competitor.displayName || item.competitor.username}
                              </div>
                              <div className="text-sm text-gray-500">
                                @{item.competitor.username}
                              </div>
                            </div>
                          </div>
                        </td>
                        
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                            {getMetricLabel(item.benchmark.metric)}
                          </span>
                        </td>
                        
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {formatValue(item.benchmark.userValue, item.benchmark.metric)}
                        </td>
                        
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatValue(item.benchmark.competitorValue, item.benchmark.metric)}
                        </td>
                        
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPerformanceColor(item.performance)}`}>
                            <Icon className="w-3 h-3 mr-1" />
                            {item.performance.charAt(0).toUpperCase() + item.performance.slice(1)}
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-sm p-12 text-center">
          <UsersIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500 mb-2">No competitive benchmarks available</p>
          <p className="text-sm text-gray-400 mb-6">
            Add competitors to your tracking list to see performance comparisons
          </p>
          
          <div className="flex justify-center space-x-4">
            <a
              href="/competitors"
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              data-rounded="default"
            >
              Add Competitors
            </a>
            
            <button
              onClick={onGenerateAnalysis}
              className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
              data-rounded="default"
            >
              Generate Analysis
            </button>
          </div>
        </div>
      )}
    </div>
  );
}