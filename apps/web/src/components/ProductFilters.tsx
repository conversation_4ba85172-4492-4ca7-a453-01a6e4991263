'use client';

import { useState } from 'react';
import {
  Settings01 as SettingsIcon,
  X as XIcon,
  ChevronDown as ChevronDownIcon
} from '@untitled-ui/icons-react';

interface FilterProps {
  category: string;
  minTrendScore: number;
  minGMV: number;
  maxGMV: number;
  sortBy: 'trendScore' | 'soldIn24h' | 'estimatedGMV' | 'commissionRate';
  sortOrder: 'asc' | 'desc';
  page: number;
  limit: number;
}

interface ProductFiltersProps {
  filters: FilterProps;
  onFilterChange: (filters: Partial<FilterProps>) => void;
}

const categories = [
  { id: '', label: 'All Categories' },
  { id: 'Electronics', label: 'Electronics' },
  { id: 'Fashion', label: 'Fashion' },
  { id: 'Beauty', label: 'Beauty' },
  { id: 'Home & Garden', label: 'Home & Garden' },
  { id: 'Sports & Outdoors', label: 'Sports & Outdoors' },
  { id: 'Health & Wellness', label: 'Health & Wellness' }
];

const trendScores = [
  { id: 0, label: 'Any Score' },
  { id: 40, label: '40+' },
  { id: 60, label: '60+' },
  { id: 80, label: '80+' }
];

const sortOptions = [
  { id: 'trendScore', label: 'Trend Score' },
  { id: 'soldIn24h', label: '24h Sales' },
  { id: 'estimatedGMV', label: 'Estimated GMV' },
  { id: 'commissionRate', label: 'Commission Rate' }
];

const gmvRanges = [
  { id: 'any', label: 'Any GMV', min: 0, max: 0 },
  { id: 'low', label: '$0-1K', min: 0, max: 1000 },
  { id: 'medium', label: '$1K-10K', min: 1000, max: 10000 },
  { id: 'high', label: '$10K-100K', min: 10000, max: 100000 },
  { id: 'very-high', label: '$100K+', min: 100000, max: 0 }
];

export function ProductFilters({ filters, onFilterChange }: ProductFiltersProps) {
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [selectedGMVRange, setSelectedGMVRange] = useState('any');

  const handleCategoryChange = (category: string) => {
    onFilterChange({ category });
  };

  const handleTrendScoreChange = (minTrendScore: number) => {
    onFilterChange({ minTrendScore });
  };

  const handleGMVRangeChange = (rangeId: string) => {
    const range = gmvRanges.find(r => r.id === rangeId);
    if (range) {
      setSelectedGMVRange(rangeId);
      onFilterChange({
        minGMV: range.min,
        maxGMV: range.max
      });
    }
  };

  const handleSortChange = (sortBy: FilterProps['sortBy']) => {
    onFilterChange({ sortBy });
  };

  const handleSortOrderChange = (sortOrder: FilterProps['sortOrder']) => {
    onFilterChange({ sortOrder });
  };

  const clearFilters = () => {
    setSelectedGMVRange('any');
    onFilterChange({
      category: '',
      minTrendScore: 0,
      minGMV: 0,
      maxGMV: 0,
      sortBy: 'trendScore',
      sortOrder: 'desc',
    });
  };

  const hasActiveFilters = filters.category || filters.minTrendScore > 0;

  return (
    <div className="space-y-4">
      {/* Category Filter - Toggle Style */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 font-sans">Category</h3>
          {hasActiveFilters && (
            <button
              onClick={clearFilters}
              className="text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors font-sans"
            >
              Clear all
            </button>
          )}
        </div>
        <div className="flex flex-wrap gap-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
          {categories.map(category => (
            <button
              key={category.id}
              onClick={() => handleCategoryChange(category.id)}
              className={`
                px-3 py-1.5 rounded-md text-sm font-medium transition-all duration-200
                ${filters.category === category.id
                  ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-white/50 dark:hover:bg-gray-700/50'
                }
              `}
              style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
            >
              {category.label}
            </button>
          ))}
        </div>
      </div>

      {/* Sort & Score Filters */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-6">
          {/* Sort By */}
          <div>
            <label className="text-sm font-medium text-gray-700 font-sans mb-2 block">Sort by</label>
            <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
              {sortOptions.map(option => (
                <button
                  key={option.id}
                  onClick={() => handleSortChange(option.id as FilterProps['sortBy'])}
                  className={`
                    px-3 py-1.5 rounded-md text-sm font-medium transition-all duration-200
                    ${filters.sortBy === option.id
                      ? 'bg-white text-gray-900 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                    }
                  `}
                  style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
                >
                  {option.label}
                </button>
              ))}
            </div>
          </div>

          {/* Sort Order */}
          <div>
            <label className="text-sm font-medium text-gray-700 font-sans mb-2 block">Order</label>
            <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
              <button
                onClick={() => handleSortOrderChange('desc')}
                className={`
                  px-3 py-1.5 rounded-md text-sm font-medium transition-all duration-200
                  ${filters.sortOrder === 'desc'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                  }
                `}
                style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
              >
                High to Low
              </button>
              <button
                onClick={() => handleSortOrderChange('asc')}
                className={`
                  px-3 py-1.5 rounded-md text-sm font-medium transition-all duration-200
                  ${filters.sortOrder === 'asc'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                  }
                `}
                style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
              >
                Low to High
              </button>
            </div>
          </div>
        </div>

        {/* Advanced Filters Toggle */}
        <div>
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="flex items-center gap-2 px-3 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 transition-colors font-sans"
          >
            <SettingsIcon className="w-4 h-4" />
            Advanced
            <ChevronDownIcon className={`w-4 h-4 transition-transform ${showAdvanced ? 'rotate-180' : ''}`} />
          </button>
        </div>
      </div>

      {/* Advanced Filters */}
      {showAdvanced && (
        <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Trend Score Filter */}
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300 font-sans mb-2 block">Minimum Trend Score</label>
              <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
                {trendScores.map(score => (
                  <button
                    key={score.id}
                    onClick={() => handleTrendScoreChange(score.id)}
                    className={`
                      flex-1 px-2 py-1.5 rounded-md text-sm font-medium transition-all duration-200
                      ${filters.minTrendScore === score.id
                        ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm'
                        : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-white/50 dark:hover:bg-gray-700/50'
                      }
                    `}
                    style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
                  >
                    {score.label}
                  </button>
                ))}
              </div>
            </div>

            {/* GMV Range Filter */}
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300 font-sans mb-2 block">GMV Range</label>
              <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
                {gmvRanges.map(range => (
                  <button
                    key={range.id}
                    onClick={() => handleGMVRangeChange(range.id)}
                    className={`
                      flex-1 px-2 py-1.5 rounded-md text-sm font-medium transition-all duration-200
                      ${selectedGMVRange === range.id
                        ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm'
                        : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-white/50 dark:hover:bg-gray-700/50'
                      }
                    `}
                    style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
                  >
                    {range.label}
                  </button>
                ))}
              </div>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300 font-sans mb-2 block">Commission Rate</label>
              <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
                <button className="flex-1 px-2 py-1.5 rounded-md text-sm font-medium text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-white/50 dark:hover:bg-gray-700/50 font-sans">
                  Any
                </button>
                <button className="flex-1 px-2 py-1.5 rounded-md text-sm font-medium text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-white/50 dark:hover:bg-gray-700/50 font-sans">
                  5%+
                </button>
                <button className="flex-1 px-2 py-1.5 rounded-md text-sm font-medium text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-white/50 dark:hover:bg-gray-700/50 font-sans">
                  10%+
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}