'use client';

import React from 'react';
import { useNotifications } from '../../contexts/NotificationContext';
import { Card } from '../ui/Card';
import Button from '../ui/Button';
import { Badge } from '../ui/Badge';
import {
  XClose as XIcon,
  FilterLines as FilterIcon,
  CheckCircle as CheckIcon,
  TrendUp01 as TrendUpIcon,
  Users01 as UsersIcon,
  Target04 as TargetIcon,
  ArrowUpRight as ExternalIcon,
  Eye as EyeIcon
} from '@untitled-ui/icons-react';

export function NotificationPanel() {
  const { 
    notifications, 
    isOpen, 
    closePanel, 
    markAsRead, 
    markAllAsRead,
    unreadCount 
  } = useNotifications();

  if (!isOpen) return null;

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'product_alert':
        return <TargetIcon className="w-5 h-5 text-green-600" />;
      case 'competitor_alert':
        return <UsersIcon className="w-5 h-5 text-blue-600" />;
      default:
        return <TrendUpIcon className="w-5 h-5 text-gray-600" />;
    }
  };

  const getNotificationActions = (notification: any) => {
    const isSuccessAlert = notification.type === 'product_alert' &&
      notification.metadata?.trendScore &&
      notification.metadata?.threshold &&
      notification.metadata.trendScore >= notification.metadata.threshold;

    return (
      <div className="flex items-center gap-1">
        {notification.type === 'product_alert' && notification.metadata?.trendScore && (
          <Badge variant="soft" intent="success" size="sm" className="text-xs px-1 py-0">
            {notification.metadata.trendScore}%
          </Badge>
        )}
        {notification.type === 'competitor_alert' && (
          <Badge variant="soft" intent="primary" size="sm" className="text-xs px-1 py-0">
            New
          </Badge>
        )}
        {isSuccessAlert && (
          <Button
            intent="primary"
            variant="ghost"
            size="sm"
            className="text-xs px-1 py-0 h-5"
            onClick={(e) => {
              e.stopPropagation();
              // Handle view details
            }}
          >
            <EyeIcon className="w-3 h-3 mr-1" />
            Details
          </Button>
        )}
      </div>
    );
  };

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor(diff / (1000 * 60));
    
    if (hours > 0) {
      return `${hours}h ago`;
    } else if (minutes > 0) {
      return `${minutes}m ago`;
    } else {
      return 'Just now';
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-start justify-end p-4">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/40"
        onClick={closePanel}
      />

      {/* Panel */}
      <Card className="relative w-full max-w-sm h-[500px] bg-white shadow-2xl border border-gray-200 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center gap-2">
            <h2 className="text-base font-semibold text-gray-900 font-sans">Notifications</h2>
            {unreadCount > 0 && (
              <Badge variant="soft" intent="primary" size="sm">
                {unreadCount}
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-1">
            <Button intent="gray" variant="ghost" size="sm" className="p-1 h-6 w-6">
              <FilterIcon className="w-3 h-3" />
            </Button>
            <Button intent="gray" variant="ghost" size="sm" onClick={closePanel} className="p-1 h-6 w-6">
              <XIcon className="w-3 h-3" />
            </Button>
          </div>
        </div>

        {/* Mark All as Read Button */}
        {unreadCount > 0 && (
          <div className="px-4 py-2 border-b border-gray-100">
            <Button
              intent="primary"
              variant="ghost"
              size="sm"
              onClick={markAllAsRead}
              className="w-full justify-center text-xs h-6"
            >
              Mark All as Read
            </Button>
          </div>
        )}

        {/* Notifications List */}
        <div className="flex-1 overflow-y-auto">
          {notifications.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-center p-4">
              <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mb-2">
                <TargetIcon className="w-4 h-4 text-gray-400" />
              </div>
              <h3 className="text-sm font-medium text-gray-900 font-sans mb-1">No notifications</h3>
              <p className="text-xs text-gray-500 font-sans">You're all caught up!</p>
            </div>
          ) : (
            <div className="divide-y divide-gray-100">
              {notifications.map((notification, index) => (
                <div
                  key={notification.id}
                  className={`px-3 py-2 hover:bg-gray-50 transition-colors cursor-pointer ${
                    !notification.isRead ? 'bg-blue-50/30' : ''
                  }`}
                  onClick={() => markAsRead(notification.id)}
                >
                  <div className="flex items-start gap-2">
                    {/* Icon */}
                    <div className={`p-1 rounded flex-shrink-0 ${
                      notification.type === 'product_alert'
                        ? 'bg-green-50'
                        : notification.type === 'competitor_alert'
                        ? 'bg-blue-50'
                        : 'bg-gray-50'
                    }`}>
                      {notification.metadata?.avatar ? (
                        <img
                          src={notification.metadata.avatar}
                          alt=""
                          className="w-3 h-3 rounded-full"
                        />
                      ) : (
                        <div className="w-3 h-3">
                          {notification.type === 'product_alert' ? (
                            <TargetIcon className="w-3 h-3 text-green-600" />
                          ) : notification.type === 'competitor_alert' ? (
                            <UsersIcon className="w-3 h-3 text-blue-600" />
                          ) : (
                            <TrendUpIcon className="w-3 h-3 text-gray-600" />
                          )}
                        </div>
                      )}
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between gap-1">
                        <div className="flex-1">
                          <div className="flex items-center gap-1">
                            <h4 className="text-xs font-medium text-gray-900 font-sans">
                              {notification.title}
                            </h4>
                            {!notification.isRead && (
                              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full flex-shrink-0" />
                            )}
                          </div>

                          <p className="text-xs text-gray-600 font-sans mt-0.5 leading-tight">
                            {notification.message}
                          </p>

                          <div className="flex items-center justify-between mt-1">
                            <span className="text-xs text-gray-400 font-sans">
                              {formatTimestamp(notification.timestamp)}
                            </span>
                            {getNotificationActions(notification)}
                          </div>
                        </div>
                      </div>

                      {/* Dash separator for all but last item */}
                      {index < notifications.length - 1 && (
                        <div className="flex items-center mt-2 mb-1">
                          <div className="flex-1 border-t border-dashed border-gray-200"></div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-3 border-t border-gray-200">
          <Button
            intent="primary"
            variant="outlined"
            size="sm"
            className="w-full justify-center text-xs h-7"
          >
            <ExternalIcon className="w-3 h-3 mr-1" />
            View All Alerts
          </Button>
        </div>
      </Card>
    </div>
  );
}
