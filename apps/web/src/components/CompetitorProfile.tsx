'use client';

import { useState } from 'react';
import { Card } from './ui/Card';
import Button from './ui/Button';
import { Badge } from './ui/Badge';

import { Competitor } from '@xact-data/shared';
import {
  Users01 as UserIcon,
  TrendUp01 as TrendingUpIcon,
  CheckCircle as CheckCircleIcon,
  ArrowUpRight as ExternalLinkIcon,
  BarChart01 as BarChartIcon,
  MagicWand01 as SparklesIcon,
  Target04 as TargetIcon,
  Clock as ClockIcon,
  ArrowRight as ArrowRightIcon,
  PlayCircle as PlayIcon,
  Download01 as DownloadIcon,
  Bell01 as BellIcon,
  Plus as PlusIcon,
  Mail01 as MailIcon,
  Calendar as CalendarIcon,
  ArrowLeft as ArrowLeftIcon
} from '@untitled-ui/icons-react';

interface CompetitorProfileProps {
  competitor: Competitor;
  onBack: () => void;
}

export function CompetitorProfile({ competitor, onBack }: CompetitorProfileProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const [showAlertModal, setShowAlertModal] = useState(false);



  // Mock data for the profile
  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  // Mock data for analysis, playbooks, and benchmarks
  const analysisData = {
    score: 78,
    strengths: ['Strong hook in first 3 seconds', 'Excellent product demonstration', 'Clear call-to-action'],
    weaknesses: ['Slow pacing in middle section', 'Weak urgency messaging', 'Limited social proof'],
    opportunities: ['Add customer testimonials', 'Improve pacing', 'Stronger urgency triggers']
  };

  const playbooks = [
    {
      id: '1',
      title: `Outperform ${competitor.creator.username} on Wireless Earbuds`,
      difficulty: 'Medium',
      estimatedImpact: '+25% conversion rate',
      estimatedTime: '2-3 weeks',
      steps: [
        'Create stronger opening hook (0-3 seconds)',
        'Add customer testimonials for social proof',
        'Improve pacing with quick cuts',
        'Add urgency with limited-time offer',
        'Optimize CTA placement and timing'
      ]
    }
  ];

  const benchmarkData = [
    { metric: 'Retention Rate', you: '68%', competitor: '72%', industry: '65%', trend: 'up' },
    { metric: 'Watch Time', you: '45s', competitor: '52s', industry: '38s', trend: 'down' },
    { metric: 'Conversion Rate', you: '3.8%', competitor: '4.2%', industry: '3.1%', trend: 'up' },
    { metric: 'Sales/Hour', you: '$127', competitor: '$156', industry: '$98', trend: 'up' }
  ];

  return (
    <div className="space-y-6">
      {/* Back Button */}
      <div className="flex items-center gap-3 mb-4">
        <Button
          intent="gray"
          variant="outlined"
          size="sm"
          onClick={onBack}
          className="flex items-center gap-2"
        >
          <ArrowRightIcon className="w-4 h-4 rotate-180" />
          Back to Competitors
        </Button>
      </div>

      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          {competitor.creator.profileImageUrl ? (
            <img
              src={competitor.creator.profileImageUrl}
              alt={competitor.creator.username}
              className="h-12 w-12 rounded-full object-cover"
            />
          ) : (
            <div className="h-12 w-12 rounded-full bg-gray-300 dark:bg-gray-700 flex items-center justify-center">
              <UserIcon className="h-6 w-6 text-gray-600 dark:text-gray-400" />
            </div>
          )}
          <div>
            <div className="flex items-center gap-2">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 font-sans">
                {competitor.creator.displayName || competitor.creator.username}
              </h2>
              {competitor.creator.isVerified && (
                <CheckCircleIcon className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              )}
            </div>
            <p className="text-gray-600 dark:text-gray-400 font-sans">@{competitor.creator.username}</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <Button
            intent="primary"
            variant="outlined"
            size="sm"
            onClick={() => setShowAlertModal(true)}
            className="flex items-center gap-2"
          >
            <BellIcon className="w-4 h-4" />
            Create Alert
          </Button>
          <Button
            intent="gray"
            variant="outlined"
            size="sm"
            className="flex items-center gap-2"
            onClick={() => window.open(`https://tiktok.com/@${competitor.creator.username}`, '_blank')}
          >
            <ExternalLinkIcon className="w-4 h-4" />
            View on TikTok
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4" data-rounded="default">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <UserIcon className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600 font-sans">Followers</p>
              <p className="text-xl font-bold text-gray-900 font-sans">
                {formatNumber(competitor.creator.followerCount || 0)}
              </p>
            </div>
          </div>
        </Card>
        <Card className="p-4" data-rounded="default">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <TrendingUpIcon className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600 font-sans">Engagement</p>
              <p className="text-xl font-bold text-gray-900 font-sans">
                {(competitor.creator.engagementRate || 0).toFixed(1)}%
              </p>
            </div>
          </div>
        </Card>
        <Card className="p-4" data-rounded="default">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
              <TargetIcon className="w-5 h-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600 font-sans">Total GMV</p>
              <p className="text-xl font-bold text-gray-900 font-sans">
                ${formatNumber(competitor.creator.totalGMV || 0)}
              </p>
            </div>
          </div>
        </Card>
        <Card className="p-4" data-rounded="default">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
              <BarChartIcon className="w-5 h-5 text-orange-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600 font-sans">AI Score</p>
              <p className="text-xl font-bold text-gray-900 font-sans">{analysisData.score}/100</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
        <button
          onClick={() => setActiveTab('overview')}
          className={`
            flex-1 px-4 py-2 rounded-md font-medium text-sm transition-all duration-200 flex items-center justify-center gap-2
            ${activeTab === 'overview'
              ? 'bg-white text-gray-900 shadow-sm'
              : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
            }
          `}
          style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
        >
          <UserIcon className="w-4 h-4" />
          Overview
        </button>
        <button
          onClick={() => setActiveTab('analysis')}
          className={`
            flex-1 px-4 py-2 rounded-md font-medium text-sm transition-all duration-200 flex items-center justify-center gap-2
            ${activeTab === 'analysis'
              ? 'bg-white text-gray-900 shadow-sm'
              : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
            }
          `}
          style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
        >
          <BarChartIcon className="w-4 h-4" />
          AI Analysis
        </button>
        <button
          onClick={() => setActiveTab('playbooks')}
          className={`
            flex-1 px-4 py-2 rounded-md font-medium text-sm transition-all duration-200 flex items-center justify-center gap-2
            ${activeTab === 'playbooks'
              ? 'bg-white text-gray-900 shadow-sm'
              : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
            }
          `}
          style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
        >
          <SparklesIcon className="w-4 h-4" />
          Playbooks
        </button>
        <button
          onClick={() => setActiveTab('benchmarks')}
          className={`
            flex-1 px-4 py-2 rounded-md font-medium text-sm transition-all duration-200 flex items-center justify-center gap-2
            ${activeTab === 'benchmarks'
              ? 'bg-white text-gray-900 shadow-sm'
              : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
            }
          `}
          style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
        >
          <TargetIcon className="w-4 h-4" />
          Benchmarks
        </button>
        <button
          onClick={() => setActiveTab('alerts')}
          className={`
            flex-1 px-4 py-2 rounded-md font-medium text-sm transition-all duration-200 flex items-center justify-center gap-2
            ${activeTab === 'alerts'
              ? 'bg-white text-gray-900 shadow-sm'
              : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
            }
          `}
          style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
        >
          <BellIcon className="w-4 h-4" />
          Alerts
        </button>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <Card className="p-6" data-rounded="default">
          <h3 className="text-lg font-semibold text-gray-900 font-sans mb-4">Creator Overview</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 font-sans mb-2">Profile Information</h4>
              <div className="space-y-2 text-sm">
                <p className="text-gray-600 font-sans">Username: @{competitor.creator.username}</p>
                <p className="text-gray-600 font-sans">Display Name: {competitor.creator.displayName || 'N/A'}</p>
                <p className="text-gray-600 font-sans">Verified: {competitor.creator.isVerified ? 'Yes' : 'No'}</p>
                <p className="text-gray-600 font-sans">Status: {competitor.isActive ? 'Active' : 'Inactive'}</p>
              </div>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 font-sans mb-2">Performance Metrics</h4>
              <div className="space-y-2 text-sm">
                <p className="text-gray-600 font-sans">Followers: {formatNumber(competitor.creator.followerCount || 0)}</p>
                <p className="text-gray-600 font-sans">Engagement Rate: {(competitor.creator.engagementRate || 0).toFixed(1)}%</p>
                <p className="text-gray-600 font-sans">Total GMV: ${formatNumber(competitor.creator.totalGMV || 0)}</p>
                <p className="text-gray-600 font-sans">Added: {new Date(competitor.createdAt).toLocaleDateString()}</p>
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* AI Analysis Tab */}
      {activeTab === 'analysis' && (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h3 className="text-2xl font-bold text-gray-900 font-sans">AI Content Analysis</h3>
            <Badge
              intent={analysisData.score >= 80 ? 'success' : analysisData.score >= 60 ? 'warning' : 'danger'}
              size="lg"
            >
              Score: {analysisData.score}/100
            </Badge>
          </div>

          <div className="prose max-w-none">
            <div className="space-y-8">
              {/* Strengths */}
              <div>
                <h4 className="text-lg font-semibold text-green-700 font-sans mb-3">✓ Key Strengths</h4>
                <div className="space-y-2">
                  {analysisData.strengths.map((strength, index) => (
                    <p key={index} className="text-gray-700 font-sans leading-relaxed">• {strength}</p>
                  ))}
                </div>
              </div>

              {/* Weaknesses */}
              <div>
                <h4 className="text-lg font-semibold text-red-700 font-sans mb-3">⚠ Areas for Improvement</h4>
                <div className="space-y-2">
                  {analysisData.weaknesses.map((weakness, index) => (
                    <p key={index} className="text-gray-700 font-sans leading-relaxed">• {weakness}</p>
                  ))}
                </div>
              </div>

              {/* Opportunities */}
              <div>
                <h4 className="text-lg font-semibold text-blue-700 font-sans mb-3">💡 Growth Opportunities</h4>
                <div className="space-y-2">
                  {analysisData.opportunities.map((opportunity, index) => (
                    <p key={index} className="text-gray-700 font-sans leading-relaxed">• {opportunity}</p>
                  ))}
                </div>
              </div>
            </div>
          </div>

          <div className="mt-6 border-t border-dashed border-gray-300 pt-4 flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button intent="gray" variant="outlined" size="sm">
                <PlayIcon className="w-4 h-4 mr-2" />
                Watch Content
              </Button>
              <Button intent="gray" variant="outlined" size="sm">
                <DownloadIcon className="w-4 h-4 mr-2" />
                Export Report
              </Button>
            </div>
            <Button intent="primary" variant="outlined" size="sm">
              Generate Playbook
              <ArrowRightIcon className="w-4 h-4 ml-2" />
            </Button>
          </div>
        </div>
      )}

      {/* Playbooks Tab */}
      {activeTab === 'playbooks' && (
        <div className="space-y-6">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-gray-900 font-sans mb-2">Competitive Playbooks</h2>
            <p className="text-gray-600 font-sans">AI-generated strategies to outperform {competitor.creator.username}</p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {playbooks.map((playbook) => (
              <Card key={playbook.id} className="p-6 border border-gray-200 hover:border-blue-200 hover:shadow-lg transition-all duration-200" data-rounded="default">
                {/* Header */}
                <div className="flex items-start justify-between mb-6">
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-blue-50 border border-blue-100 rounded-lg flex items-center justify-center">
                      <SparklesIcon className="w-6 h-6 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="text-lg font-bold text-gray-900 font-sans mb-1">{playbook.title}</h3>
                      <p className="text-sm text-gray-600 font-sans">Competitive advantage strategy</p>
                    </div>
                  </div>
                  <Badge
                    intent={playbook.difficulty === 'Easy' ? 'success' : playbook.difficulty === 'Medium' ? 'warning' : 'danger'}
                    size="sm"
                  >
                    {playbook.difficulty}
                  </Badge>
                </div>

                {/* Impact Highlight */}
                <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
                  <div className="flex items-center gap-2 mb-2">
                    <TargetIcon className="w-5 h-5 text-blue-600" />
                    <span className="text-sm font-semibold text-blue-900 font-sans">Expected Impact</span>
                  </div>
                  <p className="text-xl font-bold text-blue-800 font-sans">{playbook.estimatedImpact}</p>
                </div>

                {/* Action Steps */}
                <div className="space-y-4 mb-6">
                  <h4 className="font-semibold text-gray-900 font-sans flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    Action Steps
                  </h4>
                  <div className="space-y-3">
                    {playbook.steps.slice(0, 3).map((step, index) => (
                      <div key={index} className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-blue-100 border border-blue-200 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                          <span className="text-xs font-bold text-blue-700">{index + 1}</span>
                        </div>
                        <p className="text-sm text-gray-700 font-sans leading-relaxed">{step}</p>
                      </div>
                    ))}
                    {playbook.steps.length > 3 && (
                      <div className="text-xs text-gray-500 font-sans ml-9">
                        +{playbook.steps.length - 3} more steps...
                      </div>
                    )}
                  </div>
                </div>

                {/* Footer */}
                <div className="border-t border-gray-100 pt-4 flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <ClockIcon className="w-4 h-4 text-gray-400" />
                    <span className="text-sm text-gray-600 font-sans">{playbook.estimatedTime}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button intent="gray" variant="ghost" size="sm">
                      <DownloadIcon className="w-4 h-4" />
                    </Button>
                    <Button intent="primary" variant="outlined" size="sm">
                      View Full Plan
                      <ArrowRightIcon className="w-4 h-4 ml-1" />
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Benchmarks Tab */}
      {activeTab === 'benchmarks' && (
        <div className="space-y-6">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-gray-900 font-sans mb-2">Performance Benchmarks</h2>
            <p className="text-gray-600 font-sans">Compare your metrics against {competitor.creator.username} and industry standards</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {benchmarkData.map((item, index) => (
              <Card key={index} className="p-6 border border-gray-200 hover:border-blue-200 hover:shadow-md transition-all duration-200" data-rounded="default">
                {/* Metric Header */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-blue-50 border border-blue-100 rounded-lg flex items-center justify-center">
                      <BarChartIcon className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 font-sans">{item.metric}</h3>
                      <p className="text-xs text-gray-500 font-sans">Performance comparison</p>
                    </div>
                  </div>
                  <div className={`p-2 rounded-lg ${item.trend === 'up' ? 'bg-green-50' : 'bg-red-50'}`}>
                    {item.trend === 'up' ? (
                      <TrendingUpIcon className="w-5 h-5 text-green-600" />
                    ) : (
                      <TrendingUpIcon className="w-5 h-5 text-red-600 rotate-180" />
                    )}
                  </div>
                </div>

                {/* Comparison Grid */}
                <div className="space-y-4">
                  {/* Your Performance */}
                  <div className="p-3 bg-blue-50 border border-blue-100 rounded-lg">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-blue-900 font-sans">Your Performance</span>
                      <span className="text-lg font-bold text-blue-800 font-sans">{item.you}</span>
                    </div>
                  </div>

                  {/* Competitor Performance */}
                  <div className="p-3 bg-red-50 border border-red-100 rounded-lg">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-red-900 font-sans">{competitor.creator.username}</span>
                      <span className="text-lg font-bold text-red-800 font-sans">{item.competitor}</span>
                    </div>
                  </div>

                  {/* Industry Average */}
                  <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700 font-sans">Industry Average</span>
                      <span className="text-lg font-bold text-gray-800 font-sans">{item.industry}</span>
                    </div>
                  </div>
                </div>

                {/* Gap Analysis */}
                <div className="mt-4 pt-4 border-t border-gray-100">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-xs font-medium text-gray-600 font-sans">
                      {item.trend === 'up' ? 'Outperforming competitor' : 'Room for improvement'}
                    </span>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Alerts Tab */}
      {activeTab === 'alerts' && (
        <div className="space-y-6">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-gray-900 font-sans mb-2">Alerts & Tracking</h2>
            <p className="text-gray-600 font-sans">Set up notifications for {competitor.creator.username}'s activities and performance changes</p>
          </div>

          {/* Quick Setup Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Competitor Launch Alerts */}
            <Card className="p-6 border border-gray-200 hover:border-blue-200 hover:shadow-md transition-all duration-200">
              <div className="flex items-start gap-4">
                <div className="p-3 bg-blue-50 border border-blue-100 rounded-lg">
                  <BellIcon className="w-6 h-6 text-blue-600" />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 font-sans mb-2">Product Launch Alerts</h3>
                  <p className="text-sm text-gray-600 font-sans mb-4">Get notified when {competitor.creator.username} promotes new products</p>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700 font-sans">New product posts</span>
                      <input type="checkbox" className="rounded text-blue-600 focus:ring-blue-500" />
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700 font-sans">Viral content (&gt;10K views)</span>
                      <input type="checkbox" defaultChecked className="rounded text-blue-600 focus:ring-blue-500" />
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700 font-sans">Price changes</span>
                      <input type="checkbox" className="rounded text-blue-600 focus:ring-blue-500" />
                    </div>
                  </div>
                </div>
              </div>
            </Card>

            {/* Performance Alerts */}
            <Card className="p-6 border border-gray-200 hover:border-blue-200 hover:shadow-md transition-all duration-200">
              <div className="flex items-start gap-4">
                <div className="p-3 bg-green-50 border border-green-100 rounded-lg">
                  <TrendingUpIcon className="w-6 h-6 text-green-600" />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 font-sans mb-2">Performance Tracking</h3>
                  <p className="text-sm text-gray-600 font-sans mb-4">Monitor significant changes in their metrics</p>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700 font-sans">Follower growth spikes</span>
                      <input type="checkbox" defaultChecked className="rounded text-blue-600 focus:ring-blue-500" />
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700 font-sans">Engagement rate changes</span>
                      <input type="checkbox" className="rounded text-blue-600 focus:ring-blue-500" />
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700 font-sans">New platform activity</span>
                      <input type="checkbox" className="rounded text-blue-600 focus:ring-blue-500" />
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </div>

          {/* Weekly Digest Settings */}
          <Card className="p-6 border border-gray-200">
            <div className="flex items-start gap-4 mb-6">
              <div className="p-3 bg-purple-50 border border-purple-100 rounded-lg">
                <MailIcon className="w-6 h-6 text-purple-600" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 font-sans mb-2">Weekly Competitor Digest</h3>
                <p className="text-sm text-gray-600 font-sans">Receive a summary of {competitor.creator.username}'s top moves and performance</p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 font-sans">Delivery Day</label>
                  <select className="w-full border border-gray-300 rounded-lg px-3 py-2 font-sans focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="monday">Monday</option>
                    <option value="tuesday">Tuesday</option>
                    <option value="wednesday">Wednesday</option>
                    <option value="thursday">Thursday</option>
                    <option value="friday">Friday</option>
                    <option value="saturday">Saturday</option>
                    <option value="sunday">Sunday</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 font-sans">Include in Digest</label>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <input type="checkbox" defaultChecked className="rounded text-blue-600 focus:ring-blue-500 mr-2" />
                      <span className="text-sm text-gray-700 font-sans">Top performing posts</span>
                    </div>
                    <div className="flex items-center">
                      <input type="checkbox" defaultChecked className="rounded text-blue-600 focus:ring-blue-500 mr-2" />
                      <span className="text-sm text-gray-700 font-sans">New products promoted</span>
                    </div>
                    <div className="flex items-center">
                      <input type="checkbox" className="rounded text-blue-600 focus:ring-blue-500 mr-2" />
                      <span className="text-sm text-gray-700 font-sans">Engagement metrics</span>
                    </div>
                    <div className="flex items-center">
                      <input type="checkbox" className="rounded text-blue-600 focus:ring-blue-500 mr-2" />
                      <span className="text-sm text-gray-700 font-sans">Platform activity summary</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 font-sans">Email Address</label>
                  <input
                    type="email"
                    placeholder="<EMAIL>"
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 font-sans focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <CalendarIcon className="w-4 h-4 text-blue-600" />
                    <span className="text-sm font-medium text-blue-900 font-sans">Next Digest</span>
                  </div>
                  <p className="text-sm text-blue-700 font-sans">Monday, Dec 18th at 9:00 AM</p>
                </div>
              </div>
            </div>

            <div className="flex justify-between items-center mt-6 pt-6 border-t border-gray-200">
              <div className="flex items-center gap-2">
                <input type="checkbox" defaultChecked className="rounded text-blue-600 focus:ring-blue-500" />
                <span className="text-sm text-gray-700 font-sans">Enable weekly digest for this competitor</span>
              </div>
              <Button intent="primary" data-rounded="default">
                Save Alert Settings
              </Button>
            </div>
          </Card>

          {/* Link to Main Alerts Page */}
          <Card className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <BellIcon className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 font-sans">Manage All Alerts</h4>
                  <p className="text-sm text-gray-600 font-sans">View and configure all your alert settings in one place</p>
                </div>
              </div>
              <Button intent="primary" variant="outlined" data-rounded="default">
                <ArrowRightIcon className="w-4 h-4 mr-2" />
                Go to Alerts Page
              </Button>
            </div>
          </Card>
        </div>
      )}

      {/* Simple Alert Modal */}
      {showAlertModal && (
        <div className="fixed inset-0 bg-black/50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
          <Card className="relative w-full max-w-md bg-white dark:bg-gray-800 shadow-2xl border border-gray-200 dark:border-gray-700">
            <div className="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 font-sans">Create Alert</h3>
              <Button
                intent="gray"
                variant="ghost"
                size="sm"
                onClick={() => setShowAlertModal(false)}
                className="h-8 w-8 p-0"
              >
                <PlusIcon className="h-4 w-4 rotate-45" />
              </Button>
            </div>

            <div className="p-4 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 font-sans">
                  Alert Type
                </label>
                <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
                  <button className="flex-1 px-3 py-2 rounded-md text-sm font-medium bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm font-sans">
                    Follower Growth
                  </button>
                  <button className="flex-1 px-3 py-2 rounded-md text-sm font-medium text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-white/50 dark:hover:bg-gray-700/50 font-sans">
                    New Content
                  </button>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 font-sans">
                  Threshold
                </label>
                <input
                  type="number"
                  placeholder="e.g., 1000"
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 font-sans"
                  data-rounded="default"
                />
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400 font-sans">
                  Get notified when follower count increases by this amount
                </p>
              </div>

              <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                <Button
                  intent="gray"
                  variant="outlined"
                  size="sm"
                  onClick={() => setShowAlertModal(false)}
                >
                  Cancel
                </Button>
                <Button
                  intent="primary"
                  variant="filled"
                  size="sm"
                  onClick={() => {
                    setShowAlertModal(false);
                    // Add alert creation logic here
                  }}
                  className="flex items-center gap-2"
                >
                  <BellIcon className="h-4 w-4" />
                  Create Alert
                </Button>
              </div>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
}
