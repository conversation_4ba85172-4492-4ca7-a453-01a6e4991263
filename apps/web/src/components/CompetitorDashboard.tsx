'use client';

import { useState, useEffect } from 'react';
import { CompetitorList } from './CompetitorList';
import { AddCompetitorModal } from './AddCompetitorModal';
import { CompetitorProfile } from './CompetitorProfile';
import { CompetitorAnalysis } from './CompetitorAnalysis';
import { CompetitorPlaybooks } from './CompetitorPlaybooks';
import { CompetitorBenchmarks } from './CompetitorBenchmarks';
import { PlusIcon } from 'lucide-react';
import { Competitor } from '@xact-data/shared';
import { apiGet, apiPost, apiDelete } from '../lib/api';
import Button from './ui/Button';

// Using shared Competitor type from @xact-data/shared

export function CompetitorDashboard() {
  const [competitors, setCompetitors] = useState<Competitor[]>([]);
  const [selectedCompetitor, setSelectedCompetitor] = useState<Competitor | null>(null);
  const [activeView, setActiveView] = useState<'list' | 'leaderboards' | 'profile' | 'analysis' | 'playbooks' | 'benchmarks'>('list');
  const [filterCategory, setFilterCategory] = useState<'all' | 'beauty' | 'fashion' | 'tech' | 'lifestyle'>('all');
  const [sortBy, setSortBy] = useState<'followers' | 'engagement' | 'gmv' | 'growth'>('followers');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchCompetitors();
  }, []);

  const fetchCompetitors = async () => {
    try {
      setLoading(true);
      const response = await apiGet('api/competitors');
      const data = await response.json();
      
      if (data.success) {
        setCompetitors(data.data.competitors || []);
      }
    } catch (error) {
      console.error('Failed to fetch competitors:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddCompetitor = async (username: string, nickname?: string) => {
    try {
      const response = await apiPost('api/competitors', { username, nickname });
      const data = await response.json();
      
      if (data.success) {
        setCompetitors(prev => [data.data, ...prev]);
        setIsAddModalOpen(false);
      } else {
        alert(data.error || 'Failed to add competitor');
      }
    } catch (error) {
      console.error('Failed to add competitor:', error);
      alert('Failed to add competitor');
    }
  };

  const handleRemoveCompetitor = async (competitorId: string) => {
    if (!confirm('Are you sure you want to remove this competitor?')) return;

    try {
      const response = await apiDelete(`api/competitors/${competitorId}`);
      const data = await response.json();
      
      if (data.success) {
        setCompetitors(prev => prev.filter(c => c.id !== competitorId));
        if (selectedCompetitor?.id === competitorId) {
          setSelectedCompetitor(null);
          setActiveView('list');
        }
      } else {
        alert(data.error || 'Failed to remove competitor');
      }
    } catch (error) {
      console.error('Failed to remove competitor:', error);
      alert('Failed to remove competitor');
    }
  };

  const handleCompetitorSelect = (competitor: Competitor) => {
    setSelectedCompetitor(competitor);
    setActiveView('profile');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Navigation */}
      <div className="flex items-center justify-between">
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
        <button
          onClick={() => setActiveView('list')}
          className={`px-4 py-2 rounded-md font-medium text-sm transition-all duration-200 ${
            activeView === 'list'
              ? 'bg-white text-gray-900 shadow-sm'
              : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
          }`}
          style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
        >
          All Competitors ({competitors.length})
        </button>
        <button
          onClick={() => setActiveView('leaderboards')}
          className={`px-4 py-2 rounded-md font-medium text-sm transition-all duration-200 ${
            activeView === 'leaderboards'
              ? 'bg-white text-gray-900 shadow-sm'
              : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
          }`}
          style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
        >
          Leaderboards
        </button>
          {selectedCompetitor && (
            <>
              <button
                onClick={() => setActiveView('profile')}
                className={`px-4 py-2 rounded-md font-medium text-sm transition-all duration-200 ${
                  activeView === 'profile'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                }`}
                style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
              >
                Profile
              </button>
              <button
                onClick={() => setActiveView('analysis')}
                className={`px-4 py-2 rounded-md font-medium text-sm transition-all duration-200 ${
                  activeView === 'analysis'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                }`}
                style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
              >
                AI Analysis
              </button>
              <button
                onClick={() => setActiveView('playbooks')}
                className={`px-4 py-2 rounded-md font-medium text-sm transition-all duration-200 ${
                  activeView === 'playbooks'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                }`}
                style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
              >
                Playbooks
              </button>
              <button
                onClick={() => setActiveView('benchmarks')}
                className={`px-4 py-2 rounded-md font-medium text-sm transition-all duration-200 ${
                  activeView === 'benchmarks'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                }`}
                style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
              >
                Benchmarks
              </button>
            </>
          )}
        </div>

        {/* Add Competitor Button */}
        <Button
          intent="primary"
          variant="filled"
          size="sm"
          onClick={() => setIsAddModalOpen(true)}
          className="flex items-center gap-2"
        >
          <PlusIcon className="w-4 h-4" />
          Add Competitor
        </Button>
      </div>

      {/* Filters */}
      {(activeView === 'list' || activeView === 'leaderboards') && (
        <div className="flex items-center gap-4">
          <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
            {['all', 'beauty', 'fashion', 'tech', 'lifestyle'].map((category) => (
              <button
                key={category}
                onClick={() => setFilterCategory(category as any)}
                className={`px-3 py-1.5 rounded-md text-sm font-medium transition-all duration-200 ${
                  filterCategory === category
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                }`}
                style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
              >
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </button>
            ))}
          </div>
          <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
            {[
              { key: 'followers', label: 'Followers' },
              { key: 'engagement', label: 'Engagement' },
              { key: 'gmv', label: 'GMV' },
              { key: 'growth', label: 'Growth' }
            ].map((sort) => (
              <button
                key={sort.key}
                onClick={() => setSortBy(sort.key as any)}
                className={`px-3 py-1.5 rounded-md text-sm font-medium transition-all duration-200 ${
                  sortBy === sort.key
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                }`}
                style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
              >
                {sort.label}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Main Content */}
      {activeView === 'list' && (
        <CompetitorList
          competitors={competitors}
          onCompetitorSelect={handleCompetitorSelect}
          onCompetitorRemove={handleRemoveCompetitor}
          onRefreshCompetitors={fetchCompetitors}
          onAddCompetitor={() => setIsAddModalOpen(true)}
        />
      )}

      {activeView === 'leaderboards' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Top Performers */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 font-sans mb-4">Top Performers</h3>
              <div className="space-y-3">
                {competitors.slice(0, 5).map((competitor, index) => (
                  <div key={competitor.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <span className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">
                        {index + 1}
                      </span>
                      <div className="flex items-center gap-2">
                        {competitor.creator.profileImageUrl ? (
                          <img
                            src={competitor.creator.profileImageUrl}
                            alt={competitor.creator.username}
                            className="w-8 h-8 rounded-full object-cover"
                          />
                        ) : (
                          <div className="w-8 h-8 rounded-full bg-gray-300"></div>
                        )}
                        <span className="font-medium text-gray-900 font-sans">@{competitor.creator.username}</span>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-900 font-sans">
                        {(competitor.creator.followerCount || 0).toLocaleString()} followers
                      </p>
                      <p className="text-xs text-gray-500 font-sans">
                        {(competitor.creator.engagementRate || 0).toFixed(1)}% engagement
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Rising Stars */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 font-sans mb-4">Rising Stars</h3>
              <div className="space-y-3">
                {competitors.slice(0, 5).map((competitor, index) => (
                  <div key={competitor.id} className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <span className="w-6 h-6 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm font-medium">
                        {index + 1}
                      </span>
                      <div className="flex items-center gap-2">
                        {competitor.creator.profileImageUrl ? (
                          <img
                            src={competitor.creator.profileImageUrl}
                            alt={competitor.creator.username}
                            className="w-8 h-8 rounded-full object-cover"
                          />
                        ) : (
                          <div className="w-8 h-8 rounded-full bg-gray-300"></div>
                        )}
                        <span className="font-medium text-gray-900 font-sans">@{competitor.creator.username}</span>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-green-700 font-sans">+24% growth</p>
                      <p className="text-xs text-gray-500 font-sans">Last 30 days</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {activeView === 'profile' && selectedCompetitor && (
        <CompetitorProfile
          competitor={selectedCompetitor}
          onBack={() => setActiveView('list')}
        />
      )}

      {activeView === 'analysis' && selectedCompetitor && (
        <CompetitorAnalysis
          competitor={selectedCompetitor}
          onBack={() => setActiveView('profile')}
        />
      )}

      {activeView === 'playbooks' && selectedCompetitor && (
        <CompetitorPlaybooks
          competitor={selectedCompetitor}
          onBack={() => setActiveView('profile')}
        />
      )}

      {activeView === 'benchmarks' && selectedCompetitor && (
        <CompetitorBenchmarks
          competitor={selectedCompetitor}
          onBack={() => setActiveView('profile')}
        />
      )}

      {/* Add Competitor Modal */}
      <AddCompetitorModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onAdd={handleAddCompetitor}
      />
    </div>
  );
}