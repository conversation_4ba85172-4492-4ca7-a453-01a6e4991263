'use client';

import React, { useState } from 'react';
import { Card } from '../ui/Card';
import Button from '../ui/Button';
import { Badge } from '../ui/Badge';
import { useNotifications } from '../../contexts/NotificationContext';
import {
  X as XIcon,
  Bell01 as BellIcon,
  Target04 as TargetIcon,
  TrendUp01 as TrendUpIcon,
  CurrencyDollar as DollarIcon
} from '@untitled-ui/icons-react';

interface CreateAlertModalProps {
  isOpen: boolean;
  onClose: () => void;
  type: 'product' | 'competitor';
  itemName: string;
  currentTrendScore?: number;
}

export function CreateAlertModal({ 
  isOpen, 
  onClose, 
  type, 
  itemName, 
  currentTrendScore 
}: CreateAlertModalProps) {
  const { addNotification } = useNotifications();
  const [alertType, setAlertType] = useState<'trend_score' | 'gmv_growth' | 'units_sold' | 'new_product' | 'live_stream' | 'viral_content'>('trend_score');
  const [threshold, setThreshold] = useState(85);
  const [gmvPercent, setGmvPercent] = useState(20);
  const [unitsThreshold, setUnitsThreshold] = useState(100);
  const [viewsThreshold, setViewsThreshold] = useState(1000000);

  if (!isOpen) return null;

  const handleCreateAlert = () => {
    let title = '';
    let message = '';
    
    if (type === 'product') {
      if (alertType === 'trend_score') {
        title = 'Trend Score Alert Created';
        message = `You'll be notified when ${itemName} hits ${threshold}% trend score`;
      } else if (alertType === 'gmv_growth') {
        title = 'GMV Growth Alert Created';
        message = `You'll be notified when ${itemName} GMV grows ${gmvPercent}% in 24h`;
      }
    } else {
      if (alertType === 'new_product') {
        title = 'New Product Alert Created';
        message = `You'll be notified when ${itemName} launches a new product`;
      } else if (alertType === 'live_stream') {
        title = 'Live Stream Alert Created';
        message = `You'll be notified when ${itemName} goes live`;
      }
    }

    addNotification({
      type: type === 'product' ? 'product_alert' : 'competitor_alert',
      title,
      message,
      metadata: {
        [type === 'product' ? 'productName' : 'competitorName']: itemName,
        threshold: alertType === 'trend_score' ? threshold : undefined,
        changePercent: alertType === 'gmv_growth' ? gmvPercent : undefined
      }
    });

    onClose();
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal */}
      <Card className="relative w-full max-w-sm bg-white shadow-2xl border border-gray-200">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center gap-2">
            <div className="p-1.5 bg-blue-50 rounded-md border border-blue-100">
              <BellIcon className="w-4 h-4 text-blue-600" />
            </div>
            <div>
              <h2 className="text-sm font-semibold text-gray-900 font-sans">Create Alert</h2>
              <p className="text-xs text-gray-500 font-sans truncate max-w-[200px]">{itemName}</p>
            </div>
          </div>
          <Button intent="gray" variant="ghost" size="sm" onClick={onClose} className="h-6 w-6 p-0">
            <XIcon className="w-3 h-3" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-4 space-y-4">
          {/* Alert Type Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 font-sans mb-3">
              Alert Type
            </label>
            <div className="grid grid-cols-1 gap-3">
              {type === 'product' ? (
                <>
                  <button
                    onClick={() => setAlertType('trend_score')}
                    className={`p-3 rounded-md border-2 text-left transition-all ${
                      alertType === 'trend_score'
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center gap-2">
                      <TargetIcon className="w-4 h-4 text-blue-600" />
                      <div>
                        <div className="text-sm font-medium text-gray-900 font-sans">Trend Score Threshold</div>
                        <div className="text-xs text-gray-500 font-sans">Alert when trend score passes threshold</div>
                      </div>
                    </div>
                  </button>

                  <button
                    onClick={() => setAlertType('gmv_growth')}
                    className={`p-3 rounded-md border-2 text-left transition-all ${
                      alertType === 'gmv_growth'
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center gap-2">
                      <TrendUpIcon className="w-4 h-4 text-green-600" />
                      <div>
                        <div className="text-sm font-medium text-gray-900 font-sans">GMV Growth</div>
                        <div className="text-xs text-gray-500 font-sans">Alert when GMV grows by percentage</div>
                      </div>
                    </div>
                  </button>

                  <button
                    onClick={() => setAlertType('units_sold')}
                    className={`p-3 rounded-md border-2 text-left transition-all ${
                      alertType === 'units_sold'
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center gap-2">
                      <DollarIcon className="w-4 h-4 text-purple-600" />
                      <div>
                        <div className="text-sm font-medium text-gray-900 font-sans">Units Sold Threshold</div>
                        <div className="text-xs text-gray-500 font-sans">Alert when daily units exceed threshold</div>
                      </div>
                    </div>
                  </button>
                </>
              ) : (
                <>
                  <button
                    onClick={() => setAlertType('new_product')}
                    className={`p-3 rounded-md border-2 text-left transition-all ${
                      alertType === 'new_product'
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center gap-2">
                      <TargetIcon className="w-4 h-4 text-blue-600" />
                      <div>
                        <div className="text-sm font-medium text-gray-900 font-sans">New Product Launch</div>
                        <div className="text-xs text-gray-500 font-sans">Alert when they launch a new product</div>
                      </div>
                    </div>
                  </button>

                  <button
                    onClick={() => setAlertType('live_stream')}
                    className={`p-3 rounded-md border-2 text-left transition-all ${
                      alertType === 'live_stream'
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center gap-2">
                      <TrendUpIcon className="w-4 h-4 text-red-600" />
                      <div>
                        <div className="text-sm font-medium text-gray-900 font-sans">Live Stream</div>
                        <div className="text-xs text-gray-500 font-sans">Alert when they go live</div>
                      </div>
                    </div>
                  </button>

                  <button
                    onClick={() => setAlertType('viral_content')}
                    className={`p-3 rounded-md border-2 text-left transition-all ${
                      alertType === 'viral_content'
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center gap-2">
                      <TrendUpIcon className="w-4 h-4 text-orange-600" />
                      <div>
                        <div className="text-sm font-medium text-gray-900 font-sans">Viral Content</div>
                        <div className="text-xs text-gray-500 font-sans">Alert when content exceeds view threshold</div>
                      </div>
                    </div>
                  </button>
                </>
              )}
            </div>
          </div>

          {/* Threshold Settings */}
          {alertType === 'trend_score' && (
            <div>
              <label className="block text-xs font-medium text-gray-700 font-sans mb-2">
                Trend Score Threshold
              </label>
              <div className="flex items-center gap-2">
                <input
                  type="range"
                  min="50"
                  max="100"
                  value={threshold}
                  onChange={(e) => setThreshold(Number(e.target.value))}
                  className="flex-1 h-1.5"
                />
                <Badge variant="soft" intent="primary" size="sm" className="text-xs px-2 py-0">
                  {threshold}%
                </Badge>
              </div>
              {currentTrendScore && (
                <p className="text-xs text-gray-500 font-sans mt-1">
                  Current: {currentTrendScore}%
                </p>
              )}
            </div>
          )}

          {alertType === 'gmv_growth' && (
            <div>
              <label className="block text-xs font-medium text-gray-700 font-sans mb-2">
                GMV Growth Percentage (24h)
              </label>
              <div className="flex items-center gap-2">
                <input
                  type="range"
                  min="5"
                  max="100"
                  value={gmvPercent}
                  onChange={(e) => setGmvPercent(Number(e.target.value))}
                  className="flex-1 h-1.5"
                />
                <Badge variant="soft" intent="success" size="sm" className="text-xs px-2 py-0">
                  {gmvPercent}%
                </Badge>
              </div>
            </div>
          )}

          {alertType === 'units_sold' && (
            <div>
              <label className="block text-xs font-medium text-gray-700 font-sans mb-2">
                Daily Units Sold Threshold
              </label>
              <div className="flex items-center gap-2">
                <input
                  type="range"
                  min="50"
                  max="1000"
                  step="50"
                  value={unitsThreshold}
                  onChange={(e) => setUnitsThreshold(Number(e.target.value))}
                  className="flex-1 h-1.5"
                />
                <Badge variant="soft" intent="primary" size="sm" className="text-xs px-2 py-0">
                  {unitsThreshold} units
                </Badge>
              </div>
            </div>
          )}

          {alertType === 'viral_content' && (
            <div>
              <label className="block text-xs font-medium text-gray-700 font-sans mb-2">
                View Count Threshold
              </label>
              <div className="flex items-center gap-2">
                <input
                  type="range"
                  min="100000"
                  max="5000000"
                  step="100000"
                  value={viewsThreshold}
                  onChange={(e) => setViewsThreshold(Number(e.target.value))}
                  className="flex-1 h-1.5"
                />
                <Badge variant="soft" intent="warning" size="sm" className="text-xs px-2 py-0">
                  {(viewsThreshold / 1000000).toFixed(1)}M views
                </Badge>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-2 p-4 border-t border-gray-200">
          <Button
            intent="gray"
            variant="outlined"
            size="sm"
            onClick={onClose}
            className="h-7 px-3 text-xs"
          >
            Cancel
          </Button>
          <Button
            intent="primary"
            variant="filled"
            size="sm"
            onClick={handleCreateAlert}
            className="h-7 px-3 text-xs"
          >
            Create Alert
          </Button>
        </div>
      </Card>
    </div>
  );
}
