'use client';

import React, { useState } from 'react';
import Button from '../ui/Button';
import { Bell01 as BellIcon } from '@untitled-ui/icons-react';
import { CreateAlertModal } from './CreateAlertModal';

interface CreateAlertButtonProps {
  type: 'product' | 'competitor';
  itemName: string;
  currentTrendScore?: number;
  size?: 'sm' | 'md';
  variant?: 'filled' | 'outlined' | 'ghost';
}

export function CreateAlertButton({ 
  type, 
  itemName, 
  currentTrendScore,
  size = 'sm',
  variant = 'outlined'
}: CreateAlertButtonProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>
      <Button
        intent="primary"
        variant={variant}
        size={size}
        onClick={() => setIsModalOpen(true)}
        className="flex items-center gap-1"
      >
        <BellIcon className="w-3 h-3" />
        <span className="text-xs">Alert</span>
      </Button>

      <CreateAlertModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        type={type}
        itemName={itemName}
        currentTrendScore={currentTrendScore}
      />
    </>
  );
}
