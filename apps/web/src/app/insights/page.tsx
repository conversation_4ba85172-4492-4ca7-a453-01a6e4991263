'use client';

import { useState } from 'react';
import { AppLayout } from '../../components/layout/AppLayout';
import { Card } from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import Badge from '../../components/ui/Badge';
import {
  CheckCircle as CheckCircleIcon,
  Calendar as CalendarViewIcon,
  LayoutGrid01 as GridIcon,
  List as ListIcon,
  Eye as EyeIcon
} from '@untitled-ui/icons-react';

type ViewType = 'cards' | 'kanban' | 'calendar' | 'insights';

interface ActionItem {
  id: string;
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  category: 'content' | 'optimization' | 'engagement' | 'analytics';
  status: 'todo' | 'in-progress' | 'done';
  dueDate?: Date;
  estimatedTime?: string;
}

const mockActionItems: ActionItem[] = [
  {
    id: '1',
    title: 'Upload 2 TikToks for Product X',
    description: 'Competitors avg. 5 videos/week, you\'re at 3.',
    impact: 'high',
    category: 'content',
    status: 'todo',
    dueDate: new Date(),
    estimatedTime: '2 hours'
  },
  {
    id: '2',
    title: 'Update product description for +5% CTR',
    description: 'Weak CTA compared to competitor benchmarks.',
    impact: 'high',
    category: 'optimization',
    status: 'todo',
    estimatedTime: '30 mins'
  },
  {
    id: '3',
    title: 'Run livestream session',
    description: 'Boost engagement and showcase new products.',
    impact: 'medium',
    category: 'engagement',
    status: 'in-progress',
    estimatedTime: '1 hour'
  },
  {
    id: '4',
    title: 'Analyze competitor hashtag strategy',
    description: 'Research trending hashtags in your niche.',
    impact: 'medium',
    category: 'analytics',
    status: 'done',
    estimatedTime: '45 mins'
  }
];

const insights = [
  "Your posting frequency is 20% below top competitors.",
  "AOV is trending +8% — double down on Product X.",
  "Retention is weak: competitors avg. 35s watch time.",
  "Your engagement rate peaked at 4.2% last Tuesday.",
  "Consider posting between 6-8 PM for better reach."
];

export default function InsightsPage() {
  const [activeView, setActiveView] = useState<ViewType>('cards');
  const [actionItems, setActionItems] = useState<ActionItem[]>(mockActionItems);

  const progress = 32; // 32% progress to $5K goal
  const completedTasks = actionItems.filter(item => item.status === 'done').length;
  const totalTasks = actionItems.length;

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400';
      case 'medium': return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'low': return 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400';
      default: return 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-400';
    }
  };

  const markAsDone = (id: string) => {
    setActionItems(prev => prev.map(item =>
      item.id === id ? { ...item, status: 'done' } : item
    ));
  };

  const insights = [
    {
      id: 1,
      type: 'opportunity',
      priority: 'high',
      title: 'Product Mix Optimization',
      description: '70% of your GMV comes from electronics. Diversifying into beauty could add $3.2K monthly.',
      impact: '+$3,200 GMV',
      action: 'Test 3 beauty products this week',
      confidence: 92
    },
    {
      id: 2,
      type: 'performance',
      priority: 'medium',
      title: 'Peak Posting Hours',
      description: 'Your conversion rate is 2.3x higher between 2-4 PM EST. Schedule more content during this window.',
      impact: '+18% conversion',
      action: 'Adjust posting schedule',
      confidence: 87
    },
    {
      id: 3,
      type: 'competitive',
      priority: 'high',
      title: 'Competitor Gap Analysis',
      description: '@TopCreator123 is outperforming you by 40% with similar products. Their hook strategy differs significantly.',
      impact: 'Close $2.1K gap',
      action: 'Analyze their top 5 hooks',
      confidence: 94
    }
  ];

  const coachCommentary = {
    overview: "You're on track for a strong month! Focus on the high-impact opportunities below to accelerate growth.",
    performance: "Your content strategy is solid, but there's room for optimization in timing and product selection.",
    competitive: "Several competitors are gaining ground. Time to level up your game with these strategic moves."
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-50 text-red-600 border-red-100';
      case 'medium': return 'bg-orange-50 text-orange-600 border-orange-100';
      case 'low': return 'bg-blue-50 text-blue-600 border-blue-100';
      default: return 'bg-gray-50 text-gray-600 border-gray-100';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'opportunity': return <TrendUpIcon className="w-5 h-5 text-blue-600" />;
      case 'performance': return <BarChartIcon className="w-5 h-5 text-blue-600" />;
      case 'competitive': return <UsersIcon className="w-5 h-5 text-blue-600" />;
      default: return <MagicWandIcon className="w-5 h-5 text-blue-600" />;
    }
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-black mb-2" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
              AI Growth Coach
            </h1>
            <p className="text-gray-600" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
              Personalized insights and recommendations to accelerate your growth
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button intent="gray" variant="outlined" data-rounded="default">
              <SparklesIcon className="w-4 h-4 mr-2" />
              Generate New Insights
            </Button>
          </div>
        </div>

        {/* Coach Commentary */}
        <Card className="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200" data-rounded="default">
          <div className="flex items-start gap-4">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
              <MagicWandIcon className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                Coach Commentary
              </h3>
              <p className="text-gray-700" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                {coachCommentary[activeTab as keyof typeof coachCommentary]}
              </p>
            </div>
          </div>
        </Card>

        {/* Navigation Tabs */}
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
          {[
            { id: 'overview', label: 'Overview', count: insights.length },
            { id: 'performance', label: 'Performance', count: 2 },
            { id: 'competitive', label: 'Competitive', count: 1 }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`
                flex-1 px-4 py-2 rounded-md font-medium text-sm transition-all duration-200
                ${activeTab === tab.id
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                }
              `}
              style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
            >
              {tab.label}
              <span className="ml-2 px-2 py-0.5 bg-gray-200 text-gray-600 rounded-full text-xs">
                {tab.count}
              </span>
            </button>
          ))}
        </div>

        {/* Insights Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {insights.map(insight => (
            <Card key={insight.id} className="p-6 transition-all duration-200 border border-gray-200 hover:border-blue-200 hover:shadow-md bg-white" data-rounded="default">
              {/* Header Section */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-start gap-4">
                  <div className="p-2.5 bg-blue-50 border border-blue-100 rounded-lg">
                    {getTypeIcon(insight.type)}
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 mb-1 font-sans">
                      {insight.title}
                    </h3>
                    <div className="flex items-center gap-2">
                      <Badge variant="soft" className={`text-xs font-medium ${getPriorityColor(insight.priority)}`}>
                        {insight.priority.toUpperCase()}
                      </Badge>
                      <span className="text-xs text-gray-500 font-sans">{insight.type}</span>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-bold text-green-600 font-sans">
                    {insight.impact}
                  </div>
                  <div className="text-xs text-gray-500 font-sans">
                    {insight.confidence}% confidence
                  </div>
                </div>
              </div>

              {/* AI Suggestion */}
              <div className="bg-gray-50 border border-gray-100 rounded-lg p-4 mb-4">
                <h4 className="text-sm font-semibold text-gray-900 mb-2 font-sans">💡 AI Suggestion</h4>
                <p className="text-sm text-gray-700 font-sans leading-relaxed">
                  {insight.description}
                </p>
              </div>

              {/* Action Item */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-sm font-medium text-gray-900 font-sans">
                    {insight.action}
                  </span>
                </div>
                <Button intent="primary" variant="outlined" size="sm">
                  Take Action
                  <ArrowRightIcon className="w-4 h-4 ml-1" />
                </Button>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </AppLayout>
  );
}